
/* global chrome */

// background.js

// Import RecordingManager - Note: Chrome extensions don't support ES6 imports in service workers
// We'll need to implement the RecordingManager inline or use a different approach

/**
 * RecordingManager - Centralized recording management
 * Handles coordination between different recording components
 */
class RecordingManager {
  constructor() {
    this.currentSession = null;
    this.state = 'IDLE'; // RecordingState.IDLE
    this.mediaRecorder = null;
    this.recordedChunks = [];
    this.screenshotInterval = null;
    this.networkEntries = [];
    this.harData = null;

    // Bind methods
    this.handleNetworkRequest = this.handleNetworkRequest.bind(this);
    this.handleNetworkResponse = this.handleNetworkResponse.bind(this);
    this.captureScreenshot = this.captureScreenshot.bind(this);
  }

  /**
   * Start a new recording session
   * @param {number} tabId - ID of the tab to record
   * @returns {Promise<string>} Session ID
   */
  async startRecording(tabId) {
    if (this.state !== 'IDLE') {
      throw new Error('Recording already in progress');
    }

    this.state = 'STARTING';

    try {
      // Get tab information
      const tab = await this.getTabInfo(tabId);

      // Create new session
      this.currentSession = {
        id: this.generateSessionId(),
        startTime: new Date(),
        endTime: null,
        tabId: tabId,
        url: tab.url,
        title: tab.title,
        logs: [],
        screenshots: [],
        videoUrl: null,
        harData: null,
        state: 'STARTING'
      };

      // Initialize recording components
      await this.initializeNetworkMonitoring();
      await this.startScreenshotCapture();
      await this.startVideoRecording();

      this.state = 'RECORDING';
      this.currentSession.state = 'RECORDING';

      // Store session in chrome storage
      await this.saveSession();

      console.log('Recording started for session:', this.currentSession.id);
      return this.currentSession.id;

    } catch (error) {
      this.state = 'ERROR';
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * Stop the current recording session
   * @returns {Promise<RecordingSession>} Completed session data
   */
  async stopRecording() {
    if (this.state !== 'RECORDING') {
      throw new Error('No recording in progress');
    }

    this.state = 'STOPPING';

    try {
      // Stop all recording components
      await this.stopNetworkMonitoring();
      this.stopScreenshotCapture();
      await this.stopVideoRecording();

      // Finalize session
      this.currentSession.endTime = new Date();
      this.currentSession.state = 'PROCESSING';

      // Process and compile data
      await this.processRecordingData();

      this.currentSession.state = 'COMPLETED';

      // Save completed session permanently
      await this.saveCompletedSession();

      const completedSession = { ...this.currentSession };
      const sessionId = completedSession.id;

      // Clear current session from active recording
      this.currentSession = null;
      this.state = 'IDLE';

      // Clear only the active recording state, keep the completed session
      await this.clearActiveSession();

      console.log('Recording stopped and saved for session:', completedSession.id);
      return completedSession;

    } catch (error) {
      this.state = 'ERROR';
      console.error('Failed to stop recording:', error);
      throw error;
    }
  }

  /**
   * Initialize network monitoring using webRequest API
   */
  async initializeNetworkMonitoring() {
    this.networkEntries = [];

    // Monitor request start
    chrome.webRequest.onBeforeRequest.addListener(
      this.handleNetworkRequest,
      { urls: ['<all_urls>'] },
      ['requestBody']
    );

    // Monitor response
    chrome.webRequest.onCompleted.addListener(
      this.handleNetworkResponse,
      { urls: ['<all_urls>'] },
      ['responseHeaders']
    );
  }

  /**
   * Stop network monitoring
   */
  async stopNetworkMonitoring() {
    chrome.webRequest.onBeforeRequest.removeListener(this.handleNetworkRequest);
    chrome.webRequest.onCompleted.removeListener(this.handleNetworkResponse);

    // Generate HAR data
    this.generateHarData();
  }

  /**
   * Handle network request
   * @param {Object} details - Request details
   */
  handleNetworkRequest(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = {
      requestId: details.requestId,
      url: details.url,
      method: details.method,
      startTime: details.timeStamp,
      requestBody: details.requestBody,
      tabId: details.tabId
    };

    this.networkEntries.push(entry);

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_REQUEST',
      message: `${details.method} ${details.url}`,
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Handle network response
   * @param {Object} details - Response details
   */
  handleNetworkResponse(details) {
    if (!this.currentSession || this.state !== 'RECORDING') return;

    const entry = this.networkEntries.find(e => e.requestId === details.requestId);
    if (entry) {
      entry.status = details.statusCode;
      entry.responseHeaders = details.responseHeaders;
      entry.endTime = details.timeStamp;
      entry.size = details.responseSize || 0;
    }

    // Add to session logs
    this.addLogEntry({
      type: 'NETWORK_RESPONSE',
      message: `${details.statusCode} ${details.url}`,
      status: details.statusCode >= 400 ? 'error' : 'success',
      timestamp: new Date(details.timeStamp)
    });
  }

  /**
   * Start periodic screenshot capture
   */
  async startScreenshotCapture() {
    this.screenshotInterval = setInterval(async () => {
      try {
        await this.captureScreenshot();
      } catch (error) {
        console.warn('Failed to capture screenshot:', error);
      }
    }, 5000); // Capture every 5 seconds
  }

  /**
   * Stop screenshot capture
   */
  stopScreenshotCapture() {
    if (this.screenshotInterval) {
      clearInterval(this.screenshotInterval);
      this.screenshotInterval = null;
    }
  }

  /**
   * Capture a screenshot of the active tab
   */
  async captureScreenshot(isManual = false) {
    return new Promise((resolve, reject) => {
      chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
        if (chrome.runtime.lastError || !dataUrl) {
          reject(new Error(chrome.runtime.lastError?.message || 'Screenshot failed'));
          return;
        }

        let screenshot;
        if (isManual) {
          // Manual screenshots use object format with metadata
          screenshot = {
            dataUrl: dataUrl,
            timestamp: new Date(),
            isManual: true,
            id: Date.now() + Math.random()
          };
        } else {
          // Automatic screenshots use string format for backward compatibility
          screenshot = dataUrl;
        }

        this.currentSession.screenshots.push(screenshot);
        this.addLogEntry({
          type: 'SCREENSHOT',
          message: isManual ? 'Manual screenshot captured' : 'Automatic screenshot captured',
          timestamp: new Date(),
          isManual: isManual
        });

        resolve(isManual ? screenshot : { dataUrl: screenshot, isManual: false });
      });
    });
  }

  /**
   * Capture a manual screenshot triggered by user
   */
  async captureManualScreenshot() {
    try {
      const screenshot = await this.captureScreenshot(true);

      // Save manual screenshot to downloads if configured
      await this.saveManualScreenshot(screenshot);

      return screenshot;
    } catch (error) {
      console.error('BugReplay: Failed to capture manual screenshot:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Failed to capture manual screenshot: ${error.message}`,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Save manual screenshot to local storage (downloads)
   */
  async saveManualScreenshot(screenshot) {
    try {
      if (!this.currentSession || !screenshot) return;

      const fileName = `bugreplay-screenshot-${this.currentSession.id}-${Date.now()}.png`;

      // Convert data URL to blob
      const response = await fetch(screenshot.dataUrl);
      const blob = await response.blob();

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      this.addLogEntry({
        type: 'SYSTEM',
        message: `Manual screenshot saved as ${fileName}`,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('BugReplay: Error saving manual screenshot:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Error saving manual screenshot: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Start video recording using desktopCapture API
   */
  async startVideoRecording() {
    try {
      // For now, just log that video recording would start
      // Video recording requires complex permission handling
      this.addLogEntry({
        type: 'SYSTEM',
        message: 'Video recording feature initialized (enhanced screenshots active)',
        timestamp: new Date()
      });

      console.log('BugReplay: Video recording feature ready');
    } catch (error) {
      console.error('BugReplay: Failed to start video recording:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Failed to start video recording: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Stop video recording
   */
  async stopVideoRecording() {
    try {
      // For now, just log that video recording would stop
      this.addLogEntry({
        type: 'SYSTEM',
        message: 'Video recording session completed (screenshots captured)',
        timestamp: new Date()
      });

      console.log('BugReplay: Video recording session ended');
    } catch (error) {
      console.error('BugReplay: Error stopping video recording:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Error stopping video recording: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Handle video recording data from content script
   */
  handleVideoRecordingData(videoData) {
    try {
      if (this.currentSession && videoData) {
        this.currentSession.videoUrl = videoData.videoUrl;
        this.currentSession.videoSize = videoData.videoSize;

        this.addLogEntry({
          type: 'SYSTEM',
          message: `Video recording processed (${this.formatFileSize(videoData.videoSize)})`,
          timestamp: new Date()
        });

        console.log('BugReplay: Video recording data received, size:', videoData.videoSize);
      }
    } catch (error) {
      console.error('BugReplay: Error handling video recording data:', error);
      this.addLogEntry({
        type: 'ERROR',
        message: `Error processing video recording: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Add a log entry to the current session
   * @param {Object} logEntry - Log entry to add
   */
  addLogEntry(logEntry) {
    if (!this.currentSession) return;

    const entry = {
      id: Date.now() + Math.random(),
      timestamp: logEntry.timestamp ? (typeof logEntry.timestamp === 'string' ? new Date(logEntry.timestamp) : logEntry.timestamp) : new Date(),
      ...logEntry
    };

    this.currentSession.logs.push(entry);

    // Save session periodically to persist logs
    this.saveSession().catch(error => {
      console.warn('BugReplay: Failed to save session after adding log:', error);
    });
  }

  /**
   * Generate HAR (HTTP Archive) data from network entries
   */
  generateHarData() {
    this.currentSession.harData = {
      log: {
        version: '1.2',
        creator: {
          name: 'BugReplay',
          version: '1.0.0'
        },
        entries: this.networkEntries.map(entry => ({
          startedDateTime: new Date(entry.startTime).toISOString(),
          time: entry.endTime ? entry.endTime - entry.startTime : 0,
          request: {
            method: entry.method,
            url: entry.url,
            httpVersion: 'HTTP/1.1',
            headers: [],
            queryString: [],
            postData: entry.requestBody ? {
              mimeType: 'application/json',
              text: JSON.stringify(entry.requestBody)
            } : undefined,
            headersSize: -1,
            bodySize: -1
          },
          response: {
            status: entry.status || 0,
            statusText: '',
            httpVersion: 'HTTP/1.1',
            headers: entry.responseHeaders || [],
            content: {
              size: entry.size || 0,
              mimeType: 'application/octet-stream'
            },
            redirectURL: '',
            headersSize: -1,
            bodySize: entry.size || 0
          },
          cache: {},
          timings: {
            send: 0,
            wait: entry.endTime ? entry.endTime - entry.startTime : 0,
            receive: 0
          }
        }))
      }
    };
  }

  /**
   * Process and finalize recording data
   */
  async processRecordingData() {
    // Additional processing can be added here
    console.log('Processing recording data for session:', this.currentSession.id);
  }

  /**
   * Save session to chrome storage
   */
  async saveSession() {
    if (!this.currentSession) return;

    const sessionData = { ...this.currentSession };
    await chrome.storage.local.set({
      [`session_${sessionData.id}`]: sessionData,
      'currentSessionId': sessionData.id,
      'recordingState': this.state
    });
  }

  /**
   * Restore session from chrome storage
   * @param {string} sessionId - Session ID to restore
   */
  async restoreSession(sessionId) {
    try {
      const result = await chrome.storage.local.get([`session_${sessionId}`]);
      const sessionData = result[`session_${sessionId}`];

      if (!sessionData) {
        console.warn('BugReplay: No session data found for ID:', sessionId);
        return false;
      }

      // Restore session data
      this.currentSession = {
        ...sessionData,
        startTime: new Date(sessionData.startTime),
        endTime: sessionData.endTime ? new Date(sessionData.endTime) : null
      };

      this.state = sessionData.state || 'RECORDING';

      // Restore recording components if session was recording
      if (this.state === 'RECORDING') {
        await this.initializeNetworkMonitoring();
        await this.startScreenshotCapture();
        console.log('BugReplay: Recording session restored successfully:', sessionId);
      }

      return true;
    } catch (error) {
      console.error('BugReplay: Error restoring session:', error);
      return false;
    }
  }

  /**
   * Save completed session permanently
   */
  async saveCompletedSession() {
    if (!this.currentSession) return;

    const sessionData = {
      ...this.currentSession,
      completedAt: new Date(),
      size: this.calculateSessionSize(),
      status: 'completed'
    };

    try {
      // Save to completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];

      // Add new session to the list
      completedSessions.push({
        id: sessionData.id,
        title: sessionData.title,
        url: sessionData.url,
        startTime: sessionData.startTime,
        endTime: sessionData.endTime,
        completedAt: sessionData.completedAt,
        duration: sessionData.endTime ? sessionData.endTime - sessionData.startTime : 0,
        size: sessionData.size,
        status: sessionData.status,
        screenshotCount: sessionData.screenshots?.length || 0,
        logCount: sessionData.logs?.length || 0,
        networkRequestCount: sessionData.harData?.log?.entries?.length || 0
      });

      // Save both the session data and the updated list
      await chrome.storage.local.set({
        [`completed_session_${sessionData.id}`]: sessionData,
        'completedSessions': completedSessions
      });

      console.log('BugReplay: Session saved permanently:', sessionData.id);
    } catch (error) {
      console.error('BugReplay: Error saving completed session:', error);
    }
  }

  /**
   * Clear only active session data, keep completed sessions
   */
  async clearActiveSession() {
    try {
      await chrome.storage.local.remove([
        'currentSessionId',
        'recordingState'
      ]);
    } catch (error) {
      console.error('BugReplay: Error clearing active session:', error);
    }
  }

  /**
   * Calculate session data size (approximate)
   * @returns {number} Size in bytes
   */
  calculateSessionSize() {
    if (!this.currentSession) return 0;

    let size = 0;

    // Calculate approximate size
    size += JSON.stringify(this.currentSession.logs || []).length;
    size += (this.currentSession.screenshots || []).reduce((acc, screenshot) => {
      if (typeof screenshot === 'string') {
        return acc + screenshot.length; // String format (automatic screenshots)
      } else if (screenshot && screenshot.dataUrl) {
        return acc + screenshot.dataUrl.length; // Object format (manual screenshots)
      }
      return acc;
    }, 0);
    size += JSON.stringify(this.currentSession.harData || {}).length;

    return size;
  }

  /**
   * Get all completed sessions
   * @returns {Promise<Array>} List of completed sessions
   */
  async getCompletedSessions() {
    try {
      const result = await chrome.storage.local.get(['completedSessions']);
      return result.completedSessions || [];
    } catch (error) {
      console.error('BugReplay: Error getting completed sessions:', error);
      return [];
    }
  }

  /**
   * Get a specific completed session by ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object|null>} Session data or null
   */
  async getCompletedSession(sessionId) {
    try {
      const result = await chrome.storage.local.get([`completed_session_${sessionId}`]);
      return result[`completed_session_${sessionId}`] || null;
    } catch (error) {
      console.error('BugReplay: Error getting completed session:', error);
      return null;
    }
  }

  /**
   * Delete a completed session
   * @param {string} sessionId - Session ID to delete
   * @returns {Promise<boolean>} Success status
   */
  async deleteCompletedSession(sessionId) {
    try {
      // Remove session data
      await chrome.storage.local.remove([`completed_session_${sessionId}`]);

      // Update completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];
      const updatedSessions = completedSessions.filter(session => session.id !== sessionId);

      await chrome.storage.local.set({ 'completedSessions': updatedSessions });

      console.log('BugReplay: Session deleted:', sessionId);
      return true;
    } catch (error) {
      console.error('BugReplay: Error deleting session:', error);
      return false;
    }
  }

  /**
   * Delete multiple completed sessions
   * @param {string[]} sessionIds - Array of session IDs to delete
   * @returns {Promise<number>} Number of sessions deleted
   */
  async deleteMultipleSessions(sessionIds) {
    let deletedCount = 0;

    for (const sessionId of sessionIds) {
      const success = await this.deleteCompletedSession(sessionId);
      if (success) deletedCount++;
    }

    return deletedCount;
  }

  /**
   * Get storage usage statistics
   * @returns {Promise<Object>} Storage usage info
   */
  async getStorageUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES || 5242880; // 5MB default

      const sessions = await this.getCompletedSessions();

      return {
        used: usage,
        quota: quota,
        available: quota - usage,
        percentage: Math.round((usage / quota) * 100),
        sessionCount: sessions.length,
        averageSessionSize: sessions.length > 0 ? Math.round(usage / sessions.length) : 0
      };
    } catch (error) {
      console.error('BugReplay: Error getting storage usage:', error);
      return {
        used: 0,
        quota: 5242880,
        available: 5242880,
        percentage: 0,
        sessionCount: 0,
        averageSessionSize: 0
      };
    }
  }

  /**
   * Import a session from external data
   * @param {Object} sessionData - Session data to import
   * @returns {Promise<Object>} Import result
   */
  async importSession(sessionData) {
    try {
      // Generate new ID if there's a conflict
      let importId = sessionData.id;
      const existingSessions = await this.getCompletedSessions();
      const existingIds = existingSessions.map(s => s.id);

      if (existingIds.includes(importId)) {
        // Generate new ID with timestamp suffix
        const timestamp = Date.now();
        importId = `${sessionData.id}_imported_${timestamp}`;
        sessionData.id = importId;
      }

      // Ensure required fields and format
      const importedSession = {
        ...sessionData,
        id: importId,
        startTime: new Date(sessionData.startTime),
        endTime: sessionData.endTime ? new Date(sessionData.endTime) : null,
        completedAt: new Date(),
        size: this.calculateImportedSessionSize(sessionData),
        status: 'imported',
        imported: true,
        importedAt: new Date()
      };

      // Save the complete session data
      await chrome.storage.local.set({
        [`completed_session_${importId}`]: importedSession
      });

      // Add to completed sessions list
      const result = await chrome.storage.local.get(['completedSessions']);
      const completedSessions = result.completedSessions || [];

      completedSessions.push({
        id: importedSession.id,
        title: importedSession.title || 'Imported Session',
        url: importedSession.url,
        startTime: importedSession.startTime,
        endTime: importedSession.endTime,
        completedAt: importedSession.completedAt,
        duration: importedSession.endTime ?
          new Date(importedSession.endTime) - new Date(importedSession.startTime) : 0,
        size: importedSession.size,
        status: importedSession.status,
        screenshotCount: importedSession.screenshots?.length || 0,
        logCount: importedSession.logs?.length || 0,
        networkRequestCount: importedSession.harData?.log?.entries?.length || 0,
        imported: true
      });

      await chrome.storage.local.set({ completedSessions });

      console.log('BugReplay: Session imported successfully:', importId);
      return {
        success: true,
        sessionId: importId,
        message: 'Session imported successfully'
      };

    } catch (error) {
      console.error('BugReplay: Error importing session:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate size of imported session data
   * @param {Object} sessionData - Session data
   * @returns {number} Size in bytes
   */
  calculateImportedSessionSize(sessionData) {
    let size = 0;

    try {
      // Calculate approximate size
      size += JSON.stringify(sessionData.logs || []).length;
      size += (sessionData.screenshots || []).reduce((acc, screenshot) => {
        if (typeof screenshot === 'string') {
          return acc + screenshot.length; // String format (automatic screenshots)
        } else if (screenshot && screenshot.dataUrl) {
          return acc + screenshot.dataUrl.length; // Object format (manual screenshots)
        }
        return acc;
      }, 0);
      size += JSON.stringify(sessionData.harData || {}).length;
      size += JSON.stringify({
        id: sessionData.id,
        title: sessionData.title,
        url: sessionData.url,
        startTime: sessionData.startTime,
        endTime: sessionData.endTime
      }).length;
    } catch (error) {
      console.warn('BugReplay: Error calculating imported session size:', error);
    }

    return size;
  }

  /**
   * Get tab information
   * @param {number} tabId - Tab ID
   * @returns {Promise<Object>} Tab information
   */
  async getTabInfo(tabId) {
    return new Promise((resolve, reject) => {
      chrome.tabs.get(tabId, (tab) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(tab);
      });
    });
  }

  /**
   * Generate unique session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current recording state
   * @returns {string} Current state
   */
  getState() {
    return this.state;
  }

  /**
   * Get current session
   * @returns {Object|null} Current session or null
   */
  getCurrentSession() {
    return this.currentSession;
  }
}

// Initialize recording manager
const recordingManager = new RecordingManager();

// Restore recording state on startup
async function initializeExtension() {
  try {
    const result = await chrome.storage.local.get(['currentSessionId', 'recordingState']);
    if (result.currentSessionId && result.recordingState === 'RECORDING') {
      console.log('BugReplay: Restoring recording session:', result.currentSessionId);
      await recordingManager.restoreSession(result.currentSessionId);
    }
  } catch (error) {
    console.error('BugReplay: Error restoring session:', error);
  }
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onInstalled) {
  chrome.runtime.onInstalled.addListener(() => {
    console.log('BugReplay extension installed/updated.');
  });
}

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onStartup) {
  chrome.runtime.onStartup.addListener(() => {
    console.log('BugReplay extension startup.');
    initializeExtension();
  });
}

// Initialize on script load
initializeExtension();

if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // Handle recording management requests
    if (request.type === 'START_RECORDING_BACKGROUND') {
      handleStartRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'STOP_RECORDING_BACKGROUND') {
      handleStopRecording(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_RECORDING_STATE') {
      sendResponse({
        state: recordingManager.getState(),
        session: recordingManager.getCurrentSession()
      });
      return false;
    } else if (request.type === 'GET_SESSION_LOGS') {
      const session = recordingManager.getCurrentSession();
      sendResponse({
        logs: session ? session.logs : [],
        sessionId: session ? session.id : null
      });
      return false;
    } else if (request.type === 'ADD_LOG_ENTRY') {
      recordingManager.addLogEntry(request.logEntry);
      sendResponse({ success: true });
      return false;
    } else if (request.type === 'VIDEO_RECORDING_DATA') {
      recordingManager.handleVideoRecordingData(request.videoData);
      sendResponse({ success: true });
      return false;
    } else if (request.type === 'REQUEST_DESKTOP_CAPTURE') {
      handleDesktopCaptureRequest(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_COMPLETED_SESSIONS') {
      handleGetCompletedSessions(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_SESSION_DETAILS') {
      handleGetSessionDetails(request, sender, sendResponse);
      return true;
    } else if (request.type === 'DELETE_SESSION') {
      handleDeleteSession(request, sender, sendResponse);
      return true;
    } else if (request.type === 'DELETE_MULTIPLE_SESSIONS') {
      handleDeleteMultipleSessions(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_STORAGE_USAGE') {
      handleGetStorageUsage(request, sender, sendResponse);
      return true;
    } else if (request.type === 'IMPORT_SESSION') {
      handleImportSession(request, sender, sendResponse);
      return true;
    } else if (request.type === 'CAPTURE_MANUAL_SCREENSHOT') {
      handleCaptureManualScreenshot(request, sender, sendResponse);
      return true;
    } else if (request.type === 'GET_ACTIVE_TAB_INFO') {
      if (sender.tab && (sender.tab.url || sender.tab.title)) { // Message from content script, which has tab info
        sendResponse({ title: sender.tab.title, url: sender.tab.url });
      } else { // Message likely from popup, query for active tab
        if (chrome.tabs && chrome.tabs.query) {
          chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (chrome.runtime.lastError) {
              console.error('Background: Error querying active tab for GET_ACTIVE_TAB_INFO:', chrome.runtime.lastError.message);
              sendResponse({ error: `Background: Error querying active tab: ${chrome.runtime.lastError.message}` });
              return;
            }
            if (tabs && tabs[0]) {
              sendResponse({ title: tabs[0].title || tabs[0].url, url: tabs[0].url });
            } else {
              sendResponse({ error: "Background: No active tab found for GET_ACTIVE_TAB_INFO." });
            }
          });
        } else {
          sendResponse({ error: "Background: chrome.tabs.query not available for GET_ACTIVE_TAB_INFO." });
        }
      }
      return true; // Indicates that the response is sent asynchronously
    } else if (request.target === 'content_script_via_background') {
      if (chrome.tabs && chrome.tabs.query && chrome.tabs.sendMessage) {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (chrome.runtime.lastError) {
            console.error('Background: Error querying active tab for relay:', chrome.runtime.lastError.message);
            sendResponse({ error: `Background: Error querying active tab for relay: ${chrome.runtime.lastError.message}` });
            return;
          }
          if (tabs && tabs[0] && tabs[0].id) {
            const messageForContentScript = { ...request };
            delete messageForContentScript.target; // Remove the routing target property
            
            chrome.tabs.sendMessage(tabs[0].id, messageForContentScript, (responseFromContent) => {
              if (chrome.runtime.lastError) {
                // Content script might not be there or might not respond.
                // This isn't always a critical error for the background script itself,
                // but the popup needs to know.
                console.warn('Background: Error sending to content script or no response:', chrome.runtime.lastError.message);
                sendResponse({ error: `Background: Error sending to content script: ${chrome.runtime.lastError.message}`, details: responseFromContent });
                return;
              }
              sendResponse(responseFromContent);
            });
          } else {
            sendResponse({ error: "Background: No active tab found to relay message." });
          }
        });
      } else {
        sendResponse({ error: "Background: chrome.tabs API not available for relay." });
      }
      return true; // Asynchronous response
    }
    // Add more message handlers if needed, ensure 'return true' for async.
  });
}

/**
 * Ensure content script is injected into the tab
 * @param {number} tabId - Tab ID
 * @returns {Promise<boolean>} Success status
 */
async function ensureContentScriptInjected(tabId) {
  try {
    // Try to ping the content script first
    const pingResponse = await new Promise((resolve) => {
      chrome.tabs.sendMessage(tabId, { type: 'PING' }, (response) => {
        if (chrome.runtime.lastError) {
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });

    if (pingResponse && pingResponse.pong) {
      console.log('BugReplay: Content script already active');
      return true;
    }

    // Content script not responding, inject it
    console.log('BugReplay: Injecting content script into tab', tabId);
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    });

    // Wait a moment for the script to initialize
    await new Promise(resolve => setTimeout(resolve, 100));

    return true;
  } catch (error) {
    console.error('BugReplay: Failed to inject content script:', error);
    return false;
  }
}

/**
 * Handle start recording request
 * @param {Object} request - Message request
 * @param {Object} sender - Message sender
 * @param {Function} sendResponse - Response callback
 */
async function handleStartRecording(request, sender, sendResponse) {
  try {
    // Get active tab
    const tabs = await new Promise((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        resolve(tabs);
      });
    });

    if (!tabs || !tabs[0]) {
      throw new Error('No active tab found');
    }

    const tabId = tabs[0].id;

    // Ensure content script is injected
    const contentScriptReady = await ensureContentScriptInjected(tabId);
    if (!contentScriptReady) {
      throw new Error('Failed to inject content script');
    }

    const sessionId = await recordingManager.startRecording(tabId);

    // Notify content script to start recording
    chrome.tabs.sendMessage(tabId, {
      type: 'START_RECORDING_CONTENT',
      sessionId: sessionId
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.warn('Content script communication error:', chrome.runtime.lastError.message);
      } else {
        console.log('Content script recording started:', response);
      }
    });

    sendResponse({
      success: true,
      sessionId: sessionId,
      message: 'Recording started successfully'
    });
  } catch (error) {
    console.error('Failed to start recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle stop recording request
 * @param {Object} request - Message request
 * @param {Object} sender - Message sender
 * @param {Function} sendResponse - Response callback
 */
async function handleStopRecording(request, sender, sendResponse) {
  try {
    const session = await recordingManager.stopRecording();

    // Notify content script to stop recording
    if (session.tabId) {
      chrome.tabs.sendMessage(session.tabId, {
        type: 'STOP_RECORDING_CONTENT',
        sessionId: session.id
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.warn('Content script communication error during stop:', chrome.runtime.lastError.message);
        } else {
          console.log('Content script recording stopped:', response);
        }
      });
    }

    sendResponse({
      success: true,
      session: session,
      message: 'Recording stopped successfully'
    });
  } catch (error) {
    console.error('Failed to stop recording:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle get completed sessions request
 */
async function handleGetCompletedSessions(request, sender, sendResponse) {
  try {
    const sessions = await recordingManager.getCompletedSessions();
    sendResponse({
      success: true,
      sessions: sessions
    });
  } catch (error) {
    console.error('Failed to get completed sessions:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle get session details request
 */
async function handleGetSessionDetails(request, sender, sendResponse) {
  try {
    const session = await recordingManager.getCompletedSession(request.sessionId);
    sendResponse({
      success: true,
      session: session
    });
  } catch (error) {
    console.error('Failed to get session details:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle delete session request
 */
async function handleDeleteSession(request, sender, sendResponse) {
  try {
    const success = await recordingManager.deleteCompletedSession(request.sessionId);
    sendResponse({
      success: success,
      message: success ? 'Session deleted successfully' : 'Failed to delete session'
    });
  } catch (error) {
    console.error('Failed to delete session:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle delete multiple sessions request
 */
async function handleDeleteMultipleSessions(request, sender, sendResponse) {
  try {
    const deletedCount = await recordingManager.deleteMultipleSessions(request.sessionIds);
    sendResponse({
      success: true,
      deletedCount: deletedCount,
      message: `${deletedCount} sessions deleted successfully`
    });
  } catch (error) {
    console.error('Failed to delete multiple sessions:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle get storage usage request
 */
async function handleGetStorageUsage(request, sender, sendResponse) {
  try {
    const usage = await recordingManager.getStorageUsage();
    sendResponse({
      success: true,
      usage: usage
    });
  } catch (error) {
    console.error('Failed to get storage usage:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle import session request
 */
async function handleImportSession(request, sender, sendResponse) {
  try {
    const result = await recordingManager.importSession(request.sessionData);
    sendResponse(result);
  } catch (error) {
    console.error('Failed to import session:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle manual screenshot capture request
 */
async function handleCaptureManualScreenshot(request, sender, sendResponse) {
  try {
    if (recordingManager.getState() !== 'RECORDING') {
      sendResponse({
        success: false,
        error: 'No active recording session'
      });
      return;
    }

    const screenshot = await recordingManager.captureManualScreenshot();
    sendResponse({
      success: true,
      screenshot: screenshot,
      message: 'Manual screenshot captured successfully'
    });
  } catch (error) {
    console.error('Failed to capture manual screenshot:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

/**
 * Handle desktop capture request
 */
async function handleDesktopCaptureRequest(request, sender, sendResponse) {
  try {
    chrome.desktopCapture.chooseDesktopMedia(
      request.sources || ['tab'],
      sender.tab,
      (streamId) => {
        if (chrome.runtime.lastError) {
          sendResponse({
            success: false,
            error: chrome.runtime.lastError.message
          });
          return;
        }
        if (!streamId) {
          sendResponse({
            success: false,
            error: 'User cancelled screen capture'
          });
          return;
        }
        sendResponse({
          success: true,
          streamId: streamId
        });
      }
    );
  } catch (error) {
    console.error('Failed to request desktop capture:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}
