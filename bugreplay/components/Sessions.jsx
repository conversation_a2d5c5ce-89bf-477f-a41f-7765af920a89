/* global chrome */

import React, { useState, useEffect } from 'react';

/**
 * Sessions component for managing saved recording sessions
 */
export const Sessions = ({ onGenerateBugReport, onClose }) => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSessions, setSelectedSessions] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [storageUsage, setStorageUsage] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState(null);

  useEffect(() => {
    loadSessions();
    loadStorageUsage();
  }, []);

  const loadSessions = async () => {
    setLoading(true);
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        chrome.runtime.sendMessage({ type: 'GET_COMPLETED_SESSIONS' }, (response) => {
          if (response && response.success) {
            setSessions(response.sessions || []);
          } else {
            console.error('Failed to load sessions:', response?.error);
          }
          setLoading(false);
        });
      }
    } catch (error) {
      console.error('Error loading sessions:', error);
      setLoading(false);
    }
  };

  const loadStorageUsage = async () => {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        chrome.runtime.sendMessage({ type: 'GET_STORAGE_USAGE' }, (response) => {
          if (response && response.success) {
            setStorageUsage(response.usage);
          }
        });
      }
    } catch (error) {
      console.error('Error loading storage usage:', error);
    }
  };

  const handleSessionSelect = (sessionId) => {
    const newSelected = new Set(selectedSessions);
    if (newSelected.has(sessionId)) {
      newSelected.delete(sessionId);
    } else {
      newSelected.add(sessionId);
    }
    setSelectedSessions(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedSessions.size === filteredSessions.length) {
      setSelectedSessions(new Set());
    } else {
      setSelectedSessions(new Set(filteredSessions.map(s => s.id)));
    }
  };

  const handleDeleteSession = (sessionId) => {
    setSessionToDelete(sessionId);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (sessionToDelete) {
      try {
        chrome.runtime.sendMessage({ 
          type: 'DELETE_SESSION', 
          sessionId: sessionToDelete 
        }, (response) => {
          if (response && response.success) {
            setSessions(sessions.filter(s => s.id !== sessionToDelete));
            loadStorageUsage();
          } else {
            console.error('Failed to delete session:', response?.error);
          }
        });
      } catch (error) {
        console.error('Error deleting session:', error);
      }
    }
    setShowDeleteConfirm(false);
    setSessionToDelete(null);
  };

  const handleBulkDelete = async () => {
    if (selectedSessions.size === 0) return;
    
    try {
      chrome.runtime.sendMessage({ 
        type: 'DELETE_MULTIPLE_SESSIONS', 
        sessionIds: Array.from(selectedSessions) 
      }, (response) => {
        if (response && response.success) {
          setSessions(sessions.filter(s => !selectedSessions.has(s.id)));
          setSelectedSessions(new Set());
          loadStorageUsage();
        } else {
          console.error('Failed to delete sessions:', response?.error);
        }
      });
    } catch (error) {
      console.error('Error deleting sessions:', error);
    }
  };

  const handleGenerateBugReport = async (sessionId) => {
    try {
      chrome.runtime.sendMessage({ 
        type: 'GET_SESSION_DETAILS', 
        sessionId: sessionId 
      }, (response) => {
        if (response && response.success && response.session) {
          onGenerateBugReport(response.session);
        } else {
          console.error('Failed to load session details:', response?.error);
        }
      });
    } catch (error) {
      console.error('Error loading session details:', error);
    }
  };

  const handleExportSession = async (sessionId) => {
    try {
      chrome.runtime.sendMessage({ 
        type: 'GET_SESSION_DETAILS', 
        sessionId: sessionId 
      }, (response) => {
        if (response && response.success && response.session) {
          const dataStr = JSON.stringify(response.session, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `bugreplay-session-${sessionId}.json`;
          link.click();
          URL.revokeObjectURL(url);
        }
      });
    } catch (error) {
      console.error('Error exporting session:', error);
    }
  };

  const formatDuration = (duration) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredSessions = sessions
    .filter(session => {
      if (!searchTerm) return true;
      const term = searchTerm.toLowerCase();
      return (
        session.title?.toLowerCase().includes(term) ||
        session.url?.toLowerCase().includes(term) ||
        session.id.toLowerCase().includes(term)
      );
    })
    .sort((a, b) => {
      let aVal, bVal;
      switch (sortBy) {
        case 'date':
          aVal = new Date(a.completedAt || a.endTime);
          bVal = new Date(b.completedAt || b.endTime);
          break;
        case 'duration':
          aVal = a.duration || 0;
          bVal = b.duration || 0;
          break;
        case 'size':
          aVal = a.size || 0;
          bVal = b.size || 0;
          break;
        case 'title':
          aVal = a.title || '';
          bVal = b.title || '';
          break;
        default:
          aVal = a.id;
          bVal = b.id;
      }
      
      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <span className="ml-3 text-slate-300">Loading sessions...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-white">Saved Sessions</h2>
        <button
          onClick={onClose}
          className="text-slate-400 hover:text-white transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Storage Usage */}
      {storageUsage && (
        <div className="mb-4 p-3 bg-slate-800 rounded-lg border border-slate-700">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-slate-300">Storage Usage</span>
            <span className="text-sm text-slate-400">
              {formatSize(storageUsage.used)} / {formatSize(storageUsage.quota)}
            </span>
          </div>
          <div className="w-full bg-slate-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                storageUsage.percentage > 80 ? 'bg-red-500' : 
                storageUsage.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(storageUsage.percentage, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-slate-400 mt-1">
            <span>{storageUsage.sessionCount} sessions</span>
            <span>{storageUsage.percentage}% used</span>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="mb-4 space-y-3">
        {/* Search and Sort */}
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Search sessions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:border-blue-500"
          />
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
            className="px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-white focus:outline-none focus:border-blue-500"
          >
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="duration-desc">Longest First</option>
            <option value="duration-asc">Shortest First</option>
            <option value="size-desc">Largest First</option>
            <option value="size-asc">Smallest First</option>
            <option value="title-asc">Title A-Z</option>
            <option value="title-desc">Title Z-A</option>
          </select>
        </div>

        {/* Bulk Actions */}
        {sessions.length > 0 && (
          <div className="flex items-center gap-2">
            <label className="flex items-center text-sm text-slate-300">
              <input
                type="checkbox"
                checked={selectedSessions.size === filteredSessions.length && filteredSessions.length > 0}
                onChange={handleSelectAll}
                className="mr-2"
              />
              Select All ({selectedSessions.size} selected)
            </label>
            {selectedSessions.size > 0 && (
              <button
                onClick={handleBulkDelete}
                className="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-sm rounded transition-colors"
              >
                Delete Selected
              </button>
            )}
          </div>
        )}
      </div>

      {/* Sessions List */}
      <div className="flex-1 overflow-y-auto">
        {filteredSessions.length === 0 ? (
          <div className="text-center py-8 text-slate-400">
            {sessions.length === 0 ? 'No saved sessions yet' : 'No sessions match your search'}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredSessions.map((session) => (
              <SessionCard
                key={session.id}
                session={session}
                isSelected={selectedSessions.has(session.id)}
                onSelect={() => handleSessionSelect(session.id)}
                onDelete={() => handleDeleteSession(session.id)}
                onGenerateBugReport={() => handleGenerateBugReport(session.id)}
                onExport={() => handleExportSession(session.id)}
                formatDuration={formatDuration}
                formatSize={formatSize}
              />
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 max-w-md">
            <h3 className="text-lg font-bold text-white mb-4">Confirm Delete</h3>
            <p className="text-slate-300 mb-6">
              Are you sure you want to delete this session? This action cannot be undone.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Individual session card component
 */
const SessionCard = ({ 
  session, 
  isSelected, 
  onSelect, 
  onDelete, 
  onGenerateBugReport, 
  onExport,
  formatDuration,
  formatSize 
}) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'processing': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return '✓';
      case 'processing': return '⏳';
      case 'error': return '⚠';
      default: return '?';
    }
  };

  return (
    <div className={`p-4 bg-slate-800 rounded-lg border transition-colors ${
      isSelected ? 'border-blue-500 bg-slate-700' : 'border-slate-700 hover:border-slate-600'
    }`}>
      <div className="flex items-start gap-3">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onSelect}
          className="mt-1"
        />
        
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-center gap-2 mb-2">
            <span className={`text-sm ${getStatusColor(session.status)}`}>
              {getStatusIcon(session.status)}
            </span>
            <h3 className="font-medium text-white truncate">
              {session.title || 'Untitled Session'}
            </h3>
          </div>
          
          {/* URL */}
          <p className="text-sm text-slate-400 truncate mb-2">
            {session.url}
          </p>
          
          {/* Stats */}
          <div className="grid grid-cols-2 gap-4 text-xs text-slate-400 mb-3">
            <div>
              <span className="text-slate-500">Duration:</span> {formatDuration(session.duration)}
            </div>
            <div>
              <span className="text-slate-500">Size:</span> {formatSize(session.size)}
            </div>
            <div>
              <span className="text-slate-500">Screenshots:</span> {session.screenshotCount}
            </div>
            <div>
              <span className="text-slate-500">Network:</span> {session.networkRequestCount}
            </div>
          </div>
          
          {/* Date */}
          <p className="text-xs text-slate-500">
            Recorded: {new Date(session.completedAt || session.endTime).toLocaleString()}
          </p>
        </div>
        
        {/* Actions */}
        <div className="flex flex-col gap-1">
          <button
            onClick={onGenerateBugReport}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded transition-colors"
          >
            Bug Report
          </button>
          <button
            onClick={onExport}
            className="px-3 py-1 bg-green-600 hover:bg-green-500 text-white text-xs rounded transition-colors"
          >
            Export
          </button>
          <button
            onClick={onDelete}
            className="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};
